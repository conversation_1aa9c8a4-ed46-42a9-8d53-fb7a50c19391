{"name": "routeclouds-backend", "version": "1.0.0", "description": "RouteClouds E-Commerce Backend API", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "migrate": "node -e \"console.log('Running database migration...'); require('./dist/database/migrate.js');\"", "seed": "node -e \"console.log('Running database seeding...'); require('./dist/database/seed.js');\""}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "pg": "^8.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "dotenv": "^16.0.3"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/pg": "^8.10.0", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "typescript": "^5.0.0", "ts-node-dev": "^2.0.0"}}